import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';
import parse from 'html-react-parser';

import { designsTableConfig, defaultTableConfig } from '@constants';
import { useDataTableContext } from '@contexts/DataTableContext';
import { useDeleteTemplate, useUpdateTemplateNameMutation, useGetCardsTypes } from '@quires/template';
import { useLayout } from '@contexts/LayoutContext';
import useSubscriptionCheck from '../../../hooks/useSubscriptionCheck';

import { TfiTrash } from "react-icons/tfi";
import { FiEdit } from 'react-icons/fi';
import { FaSearch } from 'react-icons/fa';
import { HiDotsVertical } from 'react-icons/hi';
import { FaPlus, FaPalette } from 'react-icons/fa';
import { MdFilterList, MdCategory, MdTrendingUp, MdClear } from "react-icons/md";
import { BsCardList} from "react-icons/bs";
import { Dropdown } from 'primereact/dropdown';

import { createPortal } from 'react-dom';
import { confirmDialog, ConfirmDialog } from 'primereact/confirmdialog';
import { Toast } from 'primereact/toast';
import { Dialog } from 'primereact/dialog';
import Container from '@components/Container';
import axiosInstance from '../../../config/Axios';
import WebFont from 'webfontloader';

// CSS مخصص للـ dropdown
const dropdownStyles = `
  .p-dropdown {
    background: white !important;
    border: 1px solid #d1d5db !important;
    border-radius: 8px !important;
    color: #374151 !important;
    min-height: 44px !important;
    display: flex !important;
    align-items: center !important;
  }
  .p-dropdown-label {
    color: #374151 !important;
    font-weight: 500 !important;
    padding: 0 12px !important;
  }
  .p-dropdown-trigger {
    color: #6b7280 !important;
    width: 32px !important;
    height: 32px !important;
    border-radius: 6px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-right: 8px !important;
  }
  .p-dropdown-trigger:hover {
    background-color: #f3f4f6 !important;
  }
  .p-dropdown:hover {
    border-color: #9ca3af !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  }
  .p-dropdown:focus {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    outline: none !important;
  }
  .p-dropdown-panel,
  .templates-dropdown-panel {
    background: white !important;
    border: 1px solid #d1d5db !important;
    border-radius: 8px !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    margin-top: 4px !important;
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-item {
    color: #374151 !important;
    padding: 12px 16px !important;
    font-weight: 500 !important;
    border-bottom: 1px solid #f3f4f6 !important;
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-item:last-child {
    border-bottom: none !important;
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-item:hover {
    background-color: #f8fafc !important;
    color: #1f2937 !important;
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight {
    background-color: #eff6ff !important;
    color: #1e40af !important;
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight:hover {
    background-color: #dbeafe !important;
    color: #1d4ed8 !important;
  }
  .p-dropdown-empty-message {
    color: #6b7280 !important;
    padding: 12px 16px !important;
    font-style: italic !important;
  }

  /* Mobile FAB Styles */
  .mobile-fab-container {
    position: fixed;
    bottom: 24px;
    right: 24px;
    z-index: 1000;
  }

  .mobile-fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #a855f7 0%, #ec4899 100%);
    color: white;
    border: none;
    box-shadow: 0 4px 16px rgba(168, 85, 247, 0.4), 0 2px 8px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform: scale(1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
  }

  .mobile-fab:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow: 0 8px 24px rgba(168, 85, 247, 0.5), 0 4px 12px rgba(0, 0, 0, 0.2);
    background: linear-gradient(135deg, #9333ea 0%, #db2777 100%);
  }

  .mobile-fab:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  /* Ensure FAB stays above other elements but below burger menu */
  @media (max-width: 768px) {
    .mobile-fab-container {
      position: fixed;
      bottom: 24px;
      right: 24px;
      z-index: 1000;
    }
  }

  /* Professional Badge Rope Animations */
  @keyframes ropeSwing {
    0%, 100% { transform: rotate(-2deg); }
    50% { transform: rotate(2deg); }
  }

  @keyframes ropeGlow {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
  }

  @keyframes badgeFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-2px); }
  }

  @keyframes ropeFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-3px) rotate(1deg); }
  }

  @keyframes ropeTexture {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
  }

  @keyframes ropeWave {
    0%, 100% { transform: translateY(0px) scaleY(1); }
    25% { transform: translateY(-2px) scaleY(1.05); }
    50% { transform: translateY(-1px) scaleY(1.02); }
    75% { transform: translateY(-3px) scaleY(1.08); }
  }

  @keyframes ropeShine {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.8; }
  }

  .rope-loop {
    animation: ropeSwing 3s ease-in-out infinite;
  }

  .rope-string {
    animation: ropeGlow 2s ease-in-out infinite;
  }

  .badge-container {
    animation: badgeFloat 4s ease-in-out infinite;
  }

  .professional-rope {
    animation: ropeWave 4s ease-in-out infinite;
  }

  .rope-shine {
    animation: ropeShine 3s ease-in-out infinite;
  }

  .group:hover .rope-loop {
    animation: ropeSwing 1.5s ease-in-out infinite;
  }

  .group:hover .rope-string {
    animation: ropeGlow 1s ease-in-out infinite;
  }

  .group:hover .badge-container {
    animation: badgeFloat 2s ease-in-out infinite;
  }

  .group:hover .professional-rope {
    animation: ropeWave 2s ease-in-out infinite;
  }

  .group:hover .rope-shine {
    animation: ropeShine 1.5s ease-in-out infinite;
  }

  /* Hidden Rope Animations */
  .hidden-rope {
    animation: ropeFloat 4s ease-in-out infinite;
  }

  .rope-texture {
    animation: ropeTexture 3s ease-in-out infinite;
  }

  .group:hover .hidden-rope {
    animation: ropeFloat 2s ease-in-out infinite;
  }

  .group:hover .rope-texture {
    animation: ropeTexture 1.5s ease-in-out infinite;
  }

  /* Rope Inside Hollow Effects - Always Visible */
  .rope-inside-hollow {
    opacity: 1;
    transform: translateY(0px);
    transition: all 0.5s ease;
  }

  .group:hover .rope-inside-hollow {
    opacity: 1;
    transform: translateY(0px);
  }

  .rope-inside-hollow > div:first-child {
    animation: ropeInsideFloat 3s ease-in-out infinite;
  }

  @keyframes ropeInsideFloat {
    0%, 100% { transform: translateX(-50%) translateY(0px); }
    50% { transform: translateX(-50%) translateY(-1px); }
  }

  .group:hover .rope-inside-hollow > div:first-child {
    animation: ropeInsideFloat 1.5s ease-in-out infinite;
  }

  /* Enhanced Rope Inside Effects */
  .rope-inside-hollow > div:first-child::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .group:hover .rope-inside-hollow > div:first-child::before {
    opacity: 1;
  }

  /* Professional Rope Styles */
  .professional-rope-container {
    position: absolute;
    top: -198px;
    left: 45%;
    transform: translateX(-50%);
    z-index: 30;
    width: 32px;
    height: 160px;
    pointer-events: none;
  }

  .rope-main {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 37px;
    height: 220px;
    background: linear-gradient(135deg, #6B7280 0%, #9CA3AF 20%, #D1D5DB 40%, #E5E7EB 60%, #D1D5DB 80%, #9CA3AF 100%);
    border-radius: 16px;
    box-shadow: 
      0 2px 8px rgba(107, 114, 128, 0.4),
      inset 0 1px 2px rgba(255, 255, 255, 0.3),
      inset 0 -1px 2px rgba(0, 0, 0, 0.2);
    overflow: hidden;
  }

  .rope-texture-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      repeating-linear-gradient(
        90deg,
        transparent 0px,
        transparent 2px,
        rgba(107, 114, 128, 0.3) 2px,
        rgba(107, 114, 128, 0.3) 4px
      ),
      repeating-linear-gradient(
        0deg,
        transparent 0px,
        transparent 3px,
        rgba(205, 133, 63, 0.2) 3px,
        rgba(205, 133, 63, 0.2) 6px
      );
    border-radius: 16px;
  }

  .rope-shine-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      transparent 0%,
      rgba(255, 255, 255, 0.1) 20%,
      rgba(255, 255, 255, 0.3) 50%,
      rgba(255, 255, 255, 0.1) 80%,
      transparent 100%
    );
    border-radius: 16px;
  }

  .rope-knot {
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 18px;
    background: linear-gradient(135deg, #4B5563 0%, #6B7280 50%, #9CA3AF 100%);
    border-radius: 20px 20px 10px 10px;
    box-shadow: 
      0 2px 6px rgba(75, 85, 99, 0.4),
      inset 0 1px 2px rgba(255, 255, 255, 0.2),
      inset 0 -1px 2px rgba(0, 0, 0, 0.3);
  }

  .rope-knot::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 50%;
    transform: translateX(-50%);
    width: 32px;
    height: 10px;
    background: linear-gradient(135deg, #6B7280 0%, #9CA3AF 50%, #D1D5DB 100%);
    border-radius: 5px;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
  }



  /* Hollow Area Glow Effect */
  .hollow-area {
    transition: all 0.5s ease;
  }

  .group:hover .hollow-area {
    box-shadow: 0 0 20px rgba(107, 114, 128, 0.3), inset 0 0 10px rgba(107, 114, 128, 0.2);
  }

  .group:hover .hollow-area .rope-passage {
    box-shadow: 0 0 15px rgba(107, 114, 128, 0.4), inset 0 0 8px rgba(107, 114, 128, 0.3);
  }

  /* Rope Connection Enhancement */
  .rope-connection {
    transition: all 0.5s ease;
  }

  .group:hover .rope-connection {
    transform: translateY(-2px) scale(1.05);
  }

  .rope-entering {
    transition: all 0.5s ease;
  }

  .group:hover .rope-entering {
    box-shadow: 0 0 10px rgba(146, 64, 14, 0.3);
  }

  /* Rope to Badge Connection - Enhanced Realistic Loop */
  .rope-badge-connection {
    position: absolute;
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 32px;
    height: 16px;
    z-index: 25;
  }

  /* Main Loop Structure */
  .rope-badge-connection::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 32px;
    height: 16px;
    background: linear-gradient(135deg, #4B5563 0%, #6B7280 30%, #9CA3AF 60%, #D1D5DB 100%);
    border-radius: 16px 16px 8px 8px;
    box-shadow: 
      0 2px 6px rgba(75, 85, 99, 0.4),
      inset 0 1px 3px rgba(255, 255, 255, 0.2),
      inset 0 -1px 2px rgba(0, 0, 0, 0.3);
  }

  /* Loop Inner Structure */
  .rope-badge-connection::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 28px;
    height: 12px;
    background: linear-gradient(135deg, #6B7280 0%, #9CA3AF 40%, #D1D5DB 80%, #E5E7EB 100%);
    border-radius: 14px 14px 6px 6px;
    box-shadow: 
      inset 0 1px 2px rgba(0, 0, 0, 0.2),
      inset 0 -1px 1px rgba(255, 255, 255, 0.1);
  }

  /* Loop Inner Hollow */
  .rope-loop-inner {
    position: absolute;
    top: 4px;
    left: 4px;
    width: 24px;
    height: 8px;
    background: transparent;
    border-radius: 12px 12px 4px 4px;
    box-shadow: 
      inset 0 0 0 1px rgba(107, 114, 128, 0.3),
      inset 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  /* Loop Inner Depth */
  .rope-loop-depth {
    position: absolute;
    top: 5px;
    left: 5px;
    width: 22px;
    height: 6px;
    background: linear-gradient(135deg, #9CA3AF 0%, #D1D5DB 50%, #E5E7EB 100%);
    border-radius: 11px 11px 3px 3px;
    box-shadow: 
      inset 0 1px 1px rgba(0, 0, 0, 0.1),
      inset 0 -1px 1px rgba(255, 255, 255, 0.2);
  }

  /* Loop Texture Overlay */
  .rope-loop-texture {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
      repeating-linear-gradient(
        90deg,
        transparent 0px,
        transparent 1px,
        rgba(107, 114, 128, 0.2) 1px,
        rgba(107, 114, 128, 0.2) 2px
      ),
      repeating-linear-gradient(
        0deg,
        transparent 0px,
        transparent 2px,
        rgba(205, 133, 63, 0.1) 2px,
        rgba(205, 133, 63, 0.1) 4px
      );
    border-radius: inherit;
    opacity: 0.6;
  }

  /* Loop Shine Effect */
  .rope-loop-shine {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      transparent 0%,
      rgba(255, 255, 255, 0.1) 20%,
      rgba(255, 255, 255, 0.3) 50%,
      rgba(255, 255, 255, 0.1) 80%,
      transparent 100%
    );
    border-radius: inherit;
  }

  /* Loop Shadow */
  .rope-loop-shadow {
    position: absolute;
    bottom: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 28px;
    height: 4px;
    background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    filter: blur(2px);
  }

  /* Loop Edge Highlights */
  .rope-loop-edge-highlight {
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    height: 2px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.4) 100%);
    border-radius: 16px 16px 0 0;
  }

  /* Loop Inner Edge */
  .rope-loop-inner-edge {
    position: absolute;
    top: 3px;
    left: 3px;
    right: 3px;
    height: 1px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.3) 100%);
    border-radius: 14px 14px 0 0;
  }

  /* Loop Depth Shadow */
  .rope-loop-depth-shadow {
    position: absolute;
    top: 6px;
    left: 5px;
    right: 5px;
    height: 2px;
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.1) 50%, rgba(0, 0, 0, 0.2) 100%);
    border-radius: 0 0 11px 11px;
  }

  /* Loop Side Highlights */
  .rope-loop-side-highlight-left {
    position: absolute;
    top: 2px;
    left: 1px;
    width: 2px;
    height: 12px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.3) 100%);
    border-radius: 1px;
  }

  .rope-loop-side-highlight-right {
    position: absolute;
    top: 2px;
    right: 1px;
    width: 2px;
    height: 12px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.3) 100%);
    border-radius: 1px;
  }

  /* Loop Inner Side Highlights */
  .rope-loop-inner-side-left {
    position: absolute;
    top: 4px;
    left: 3px;
    width: 1px;
    height: 8px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.2) 100%);
    border-radius: 0.5px;
  }

  .rope-loop-inner-side-right {
    position: absolute;
    top: 4px;
    right: 3px;
    width: 1px;
    height: 8px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.2) 100%);
    border-radius: 0.5px;
  }

  /* Loop Corner Accents */
  .rope-loop-corner-top-left {
    position: absolute;
    top: 1px;
    left: 1px;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle at top left, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
    border-radius: 50%;
  }

  .rope-loop-corner-top-right {
    position: absolute;
    top: 1px;
    right: 1px;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
    border-radius: 50%;
  }

  /* Loop Inner Corner Accents */
  .rope-loop-inner-corner-left {
    position: absolute;
    top: 3px;
    left: 3px;
    width: 3px;
    height: 3px;
    background: radial-gradient(circle at top left, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
  }

  .rope-loop-inner-corner-right {
    position: absolute;
    top: 3px;
    right: 3px;
    width: 3px;
    height: 3px;
    background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
  }

  /* Enhanced Hover Effects for Loop */
  .group:hover .rope-badge-connection::before {
    background: linear-gradient(135deg, #374151 0%, #4B5563 30%, #6B7280 60%, #9CA3AF 100%);
    box-shadow: 
      0 3px 8px rgba(75, 85, 99, 0.5),
      inset 0 1px 3px rgba(255, 255, 255, 0.3),
      inset 0 -1px 2px rgba(0, 0, 0, 0.4);
    transform: scale(1.05);
    transition: all 0.3s ease;
  }

  .group:hover .rope-badge-connection::after {
    background: linear-gradient(135deg, #4B5563 0%, #6B7280 40%, #9CA3AF 80%, #D1D5DB 100%);
    box-shadow: 
      inset 0 1px 2px rgba(0, 0, 0, 0.3),
      inset 0 -1px 1px rgba(255, 255, 255, 0.2);
    transform: scale(1.02);
    transition: all 0.3s ease;
  }

  .group:hover .rope-loop-depth {
    background: linear-gradient(135deg, #6B7280 0%, #9CA3AF 50%, #D1D5DB 100%);
    box-shadow: 
      inset 0 1px 1px rgba(0, 0, 0, 0.2),
      inset 0 -1px 1px rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
  }

  .group:hover .rope-loop-texture {
    opacity: 0.8;
    transition: opacity 0.3s ease;
  }

  .group:hover .rope-loop-shine {
    background: linear-gradient(
      135deg,
      transparent 0%,
      rgba(255, 255, 255, 0.2) 20%,
      rgba(255, 255, 255, 0.4) 50%,
      rgba(255, 255, 255, 0.2) 80%,
      transparent 100%
    );
    transition: all 0.3s ease;
  }

  .group:hover .rope-loop-shadow {
    background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.4) 0%, transparent 70%);
    filter: blur(3px);
    transition: all 0.3s ease;
  }

  /* Loop Animation on Hover */
  @keyframes loopGlow {
    0%, 100% { 
      box-shadow: 
        0 2px 6px rgba(75, 85, 99, 0.4),
        inset 0 1px 3px rgba(255, 255, 255, 0.2),
        inset 0 -1px 2px rgba(0, 0, 0, 0.3);
    }
    50% { 
      box-shadow: 
        0 4px 12px rgba(75, 85, 99, 0.6),
        inset 0 1px 3px rgba(255, 255, 255, 0.3),
        inset 0 -1px 2px rgba(0, 0, 0, 0.4),
        0 0 8px rgba(107, 114, 128, 0.3);
    }
  }

  .group:hover .rope-badge-connection::before {
    animation: loopGlow 2s ease-in-out infinite;
  }



 
`;

function hideBraces(html) {
    return html.replace(/{{\s*(.*?)\s*}}/g, '<span class="var-placeholder">$1</span>');
}

function extractDesignDimensions(html) {
    if (!html) return { width: 400, height: 600 };
    
    try {
        const widthMatch = html.match(/width:\s*(\d+)px/i);
        const heightMatch = html.match(/height:\s*(\d+)px/i);
        
        const styleWidthMatch = html.match(/style="[^"]*width:\s*(\d+)px[^"]*"/i);
        const styleHeightMatch = html.match(/style="[^"]*height:\s*(\d+)px[^"]*"/i);
        
        const viewBoxMatch = html.match(/viewBox="[^"]*(\d+)\s+(\d+)[^"]*"/i);
        
        const svgWidthMatch = html.match(/<svg[^>]*width="(\d+)"[^>]*>/i);
        const svgHeightMatch = html.match(/<svg[^>]*height="(\d+)"[^>]*>/i);
        
        const divWidthMatch = html.match(/<div[^>]*width="(\d+)"[^>]*>/i);
        const divHeightMatch = html.match(/<div[^>]*height="(\d+)"[^>]*>/i);
        
        let width = 400; 
        let height = 600; 
        
        if (widthMatch) {
            width = parseInt(widthMatch[1]);
        } else if (styleWidthMatch) {
            width = parseInt(styleWidthMatch[1]);
        } else if (svgWidthMatch) {
            width = parseInt(svgWidthMatch[1]);
        } else if (divWidthMatch) {
            width = parseInt(divWidthMatch[1]);
        } else if (viewBoxMatch) {
            width = parseInt(viewBoxMatch[1]);
        }
        

        if (heightMatch) {
            height = parseInt(heightMatch[1]);
        } else if (styleHeightMatch) {
            height = parseInt(styleHeightMatch[1]);
        } else if (svgHeightMatch) {
            height = parseInt(svgHeightMatch[1]);
        } else if (divHeightMatch) {
            height = parseInt(divHeightMatch[1]);
        } else if (viewBoxMatch) {
            height = parseInt(viewBoxMatch[2]);
        }
        

        if (width < 100) width = 400;
        if (height < 100) height = 600;
        if (width > 2000) width = 400; 
        if (height > 2000) height = 600;
        
        return { width, height };
    } catch (error) {
        console.warn('Error extracting design dimensions:', error);
        return { width: 400, height: 600 };
    }
}

function calculateOptimalScale(designWidth, designHeight, containerWidth, containerHeight) {

    const scaleX = containerWidth / designWidth;
    const scaleY = containerHeight / designHeight;
    

    let optimalScale = Math.min(scaleX, scaleY);
    
    optimalScale = optimalScale * 1.2;
    
    optimalScale = Math.min(optimalScale, 1.2);
    
    optimalScale = Math.max(optimalScale, 0.4);
    
    return optimalScale;
}

// دالة لمعالجة background_style
function parseBackgroundStyle(backgroundStyle) {
    if (!backgroundStyle) return '#ffffff';
    
    try {
        // إذا كان JSON string، نحاول تحليله
        if (typeof backgroundStyle === 'string' && backgroundStyle.startsWith('{')) {
            const parsed = JSON.parse(backgroundStyle);
            const bgColor = parsed.backgroundColor || parsed.background || backgroundStyle;
            return bgColor === 'transparent' ? '#ffffff' : bgColor;
        }
        return backgroundStyle === 'transparent' ? '#ffffff' : backgroundStyle;
    } catch (error) {
        console.warn('Error parsing background_style:', error);
        return backgroundStyle === 'transparent' ? '#ffffff' : backgroundStyle;
    }
}

// دالة لمعالجة التواريخ بشكل آمن
function formatDate(dateString) {
    if (!dateString) return 'N/A';
    
    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            return 'N/A';
        }
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    } catch (error) {
        console.warn('Error formatting date:', error);
        return 'N/A';
    }
}

function TemplatesDataTable() {
    const { totalRecords, lazyParams, setLazyParams, data, dataHandler, loading } = useDataTableContext();
    const { isMobile } = useLayout();
    const navigate = useNavigate();

    const deleteTemplate = useDeleteTemplate()
    const updateTemplateName = useUpdateTemplateNameMutation();
    const [searchQuery, setSearchQuery] = useState('');
    const [cardTypeFilter, setCardTypeFilter] = useState('all');
    const { isLoading: cardTypesLoading, data: cardTypesData, error: cardTypesError } = useGetCardsTypes();
    const [mobileActionMenuOpen, setMobileActionMenuOpen] = useState(null);
    const [toastRef] = useState(React.createRef());
    const [selectedTemplate, setSelectedTemplate] = useState(null);
    const [templateModalVisible, setTemplateModalVisible] = useState(false);
    const [templateZoomLevel, setTemplateZoomLevel] = useState(1.0);
    const [editNameModal, setEditNameModal] = useState({ open: false, template: null });
    const [newTemplateName, setNewTemplateName] = useState('');

    const { subscriptionError, subscriptionLoading, noPackage } = useSubscriptionCheck();

    console.log('TemplatesDataTable render', 'selectedTemplate:', selectedTemplate, 'templateModalVisible:', templateModalVisible);

    function extractFontsFromHtml(html) {
        const fonts = new Set();
        if (!html) return [];
        try {
            const parser = new window.DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            doc.querySelectorAll('[style]').forEach(el => {
                const style = el.getAttribute('style');
                const match = /font-family:\s*([^;]+)/i.exec(style);
                if (match && match[1]) {
                    match[1].split(',').forEach(f => {
                        const clean = f.trim().replace(/['"]/g, '');
                        const mainFont = clean.split(',')[0].trim();
                        if (mainFont) fonts.add(mainFont);
                    });
                }
            });
        } catch (e) {
            console.warn('Error extracting fonts:', e);
        }
        return Array.from(fonts);
    }

    useEffect(() => {
        console.log('useEffect: templateModalVisible', templateModalVisible, 'selectedTemplate', selectedTemplate);
        if (!templateModalVisible || !selectedTemplate) {
            return;
        }
        console.log('selectedTemplate value in useEffect:', selectedTemplate);
        const fonts = extractFontsFromHtml(selectedTemplate);
        console.log('Extracted fonts:', fonts);
        if (fonts.length === 0) {
            return;
        }
        WebFont.load({
            google: {
                families: fonts,
            },
            active: () => console.log('Fonts loaded successfully'),
            inactive: () => console.log('Fonts failed to load'),
        });
    }, [templateModalVisible, selectedTemplate]);

    useEffect(() => {
        if (!data || !Array.isArray(data)) return;
        const allFonts = new Set();
        data.forEach(templateObj => {
            if (templateObj.template) {
                extractFontsFromHtml(templateObj.template).forEach(font => allFonts.add(font));
            }
        });
        const fontsArr = Array.from(allFonts);
        if (fontsArr.length > 0) {
            WebFont.load({
                google: {
                    families: fontsArr,
                }
            });
            console.log('Loaded fonts for all templates:', fontsArr);
        }
    }, [data]);

    useEffect(() => {
        const checkSubscription = async () => {
            try {
                const token = localStorage.getItem('token');
                const userId = localStorage.getItem('user_id');
                if (!token || !userId) {
                    return;
                }
                const response = await axiosInstance.get(`packages/show-package-by-id/${userId}`, {
                    headers: { Authorization: `Bearer ${token}` }
                });
                if (!response.data || Object.keys(response.data).length === 0 || response.data.error === 'No package found for this user') {
                    // setNoPackage(true); // This line was removed
                } else {
                    // setNoPackage(false); // This line was removed
                }
            } catch (error) {

                console.log('API error message:', error.response?.data?.error);
                if (error.response && error.response.data) {
                    const errMsg = error.response.data.error?.toLowerCase() || '';
                    if (error.response.data.error === "Your subscription has expired. Please renew your subscription to continue.") {
                        // Handle expired subscription
                    } else if (
                        errMsg.includes('not found') ||
                        errMsg.includes('no package') ||
                        errMsg.includes('no active package found for this user') ||
                        errMsg.includes('must have an active package')
                    ) {
                        // setNoPackage(true); // This line was removed
                    }
                }
            }
        };
        checkSubscription();
    }, []);

    useEffect(() => {
        setLazyParams({
            ...defaultTableConfig,
            ...designsTableConfig,
            rows: 12,
            url: 'get-designs-list?sort=-created_at'
        });
    }, [])

    // Get card types from API for filter options
    const cardTypeOptions = React.useMemo(() => {
        if (cardTypesLoading) return [{ label: 'Loading...', value: 'all', disabled: true }];
        if (cardTypesError) return [{ label: 'Error loading types', value: 'all', disabled: true }];
        if (!cardTypesData || cardTypesData.length === 0) return [{ label: 'All Types', value: 'all' }];
        return [
            { label: 'All Types', value: 'all' },
            ...cardTypesData.map(type => ({ label: type.name, value: type.id }))
        ];
    }, [cardTypesData, cardTypesLoading, cardTypesError]);

    // Client-side filtered data for header stats (since server-side filtering is used for pagination)
    const filteredData = React.useMemo(() => {
        if (!data || !Array.isArray(data)) return [];

        let result = data;

        // Apply search filter
        if (searchQuery.trim()) {
            result = result.filter(template =>
                (template.name || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
                (template.title || '').toLowerCase().includes(searchQuery.toLowerCase())
            );
        }

        // Apply card type filter
        if (cardTypeFilter !== 'all') {
            result = result.filter(template =>
                template.card_type_id === cardTypeFilter ||
                template.card_type?.id === cardTypeFilter
            );
        }

        return result;
    }, [data, searchQuery, cardTypeFilter]);

    // Add debounced search handler for API calls (keeping original functionality)
    useEffect(() => {
        const timeout = setTimeout(() => {
            // Build query string for filters
            let filterQuery = '';
            if (searchQuery.trim()) {
                filterQuery += `filter[name]=${encodeURIComponent(searchQuery.trim())}`;
            }
            if (cardTypeFilter !== 'all') {
                if (filterQuery.length > 0) filterQuery += '&';
                filterQuery += `filter[card_type_id]=${encodeURIComponent(cardTypeFilter)}`;
            }
            if (filterQuery.length > 0) filterQuery += '&';
            filterQuery += 'sort=-created_at';
            // Compose the url
            let url = 'get-designs-list';
            if (filterQuery.length > 0) {
                url += `?${filterQuery}`;
            }
            setLazyParams(prev => ({
                ...prev,
                url: url,
                rows: 12,
            }));
        }, 300);

        return () => clearTimeout(timeout);
    }, [searchQuery, cardTypeFilter, setLazyParams]);

    // --- ConfirmDialog handler for delete ---
    const confirmDeleteTemplate = (templateId) => {
        confirmDialog({
            group: 'headless',
            message: 'Are you sure you want to delete this template?',
            header: 'Delete Confirmation',
            icon: 'pi pi-exclamation-triangle',
            acceptClassName: 'p-button-danger',
            acceptLabel: 'Yes',
            rejectLabel: 'No',
            accept: () => handleDeleteConfirmed(templateId),
        });
    };

    // --- Actual delete handler after confirmation ---
    const handleDeleteConfirmed = async (id) => {
        try {
            await deleteTemplate.mutateAsync({ id: id }, {
                onSuccess: () => {
                    setLazyParams(prev => ({ ...prev }));
                },
                onError: () => {
                    toastRef.current && toastRef.current.show({
                        severity: 'error',
                        summary: 'Error',
                        detail: 'Failed to delete template',
                        life: 3000
                    });
                }
            });
        } catch (error) {
            toastRef.current && toastRef.current.show({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to delete template',
                life: 3000
            });
        }
    };

    // --- Edit Name Modal handler ---
    const openEditNameModal = (template) => {
        setEditNameModal({ open: true, template });
        setNewTemplateName(template.name);
    };
    const closeEditNameModal = () => {
        setEditNameModal({ open: false, template: null });
        setNewTemplateName('');
    };
    const handleEditNameSave = async () => {
        if (!editNameModal.template) return;
        try {
            await updateTemplateName.mutateAsync({ id: editNameModal.template.id, name: newTemplateName });
            setLazyParams(prev => ({ ...prev })); // Reload data
            toastRef.current && toastRef.current.show({
                severity: 'success',
                summary: 'Updated',
                detail: 'Template name updated successfully',
                life: 3000
            });
            closeEditNameModal();
        } catch (error) {
            // Check for duplicate name error
            const msg = error?.response?.data?.error;
            if (msg && msg.toLowerCase().includes('already exists')) {
                toastRef.current && toastRef.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'A design with this name already exists for your company.',
                    life: 4000
                });
            } else {
                toastRef.current && toastRef.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to update template name',
                    life: 3000
                });
            }
        }
    };

    // Mobile action menu component
    const MobileActionMenu = ({ template, isOpen, onClose }) => {
        if (!isOpen) return null;

        return createPortal(
            <div
                className="fixed inset-0 bg-black bg-opacity-60 z-[9999] flex items-center justify-center"
                style={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    backdropFilter: 'blur(8px)',
                    WebkitBackdropFilter: 'blur(8px)'
                }}
                onClick={onClose}
            >
                <div
                    className="bg-white rounded-lg p-4 m-4 w-full max-w-sm relative shadow-2xl"
                    style={{
                        zIndex: 10000,
                        backgroundColor: '#ffffff',
                        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.5)',
                        border: '1px solid rgba(0, 0, 0, 0.1)'
                    }}
                    onClick={(e) => e.stopPropagation()}
                >
                    {/* Header with template info */}
                    <div className="flex items-center mb-4 border-b pb-3">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 flex items-center justify-center mr-3 shadow-lg">
                            <span className="text-white font-bold text-lg">{template.name?.charAt(0)?.toUpperCase()}</span>
                        </div>
                        <div className="flex-1">
                            <h3 className="font-semibold text-gray-900 text-lg">{template.name}</h3>
                            <p className="text-sm text-gray-500">{template.card_type_name}</p>
                        </div>
                    </div>

                    {/* Action buttons with improved styling */}
                    <div className="space-y-3">
                        {/* Edit Template */}
                        <button
                            className="w-full flex items-center p-3 text-left bg-white hover:bg-gray-50 rounded-lg border border-gray-200 transition-all duration-200 hover:shadow-md hover:border-gray-300 hover:transform hover:scale-[1.02] active:scale-[0.98]"
                            style={{
                                minHeight: '48px'
                            }}
                            onClick={() => {
                                navigate(`/manager/design-space/${template.id}`);
                                onClose();
                            }}
                        >
                            <FiEdit className="mr-3 text-green-500" size={18} />
                            <span className="font-medium text-gray-700">Edit Template</span>
                        </button>

                        {/* Edit Name */}
                        <button
                            className="w-full flex items-center p-3 text-left bg-white hover:bg-gray-50 rounded-lg border border-gray-200 transition-all duration-200 hover:shadow-md hover:border-gray-300 hover:transform hover:scale-[1.02] active:scale-[0.98]"
                            style={{
                                minHeight: '48px'
                            }}
                            onClick={() => {
                                openEditNameModal(template);
                                onClose();
                            }}
                        >
                            <FiEdit className="mr-3 text-blue-500" size={18} />
                            <span className="font-medium text-gray-700">Edit Name</span>
                        </button>

                        {/* Delete Template */}
                        <button
                            className="w-full flex items-center p-3 text-left bg-white hover:bg-red-50 rounded-lg border border-gray-200 text-red-600 transition-all duration-200 hover:shadow-md hover:border-red-300 hover:transform hover:scale-[1.02] active:scale-[0.98]"
                            style={{
                                minHeight: '48px'
                            }}
                            onClick={() => {
                                confirmDeleteTemplate(template.id);
                                onClose();
                            }}
                        >
                            <TfiTrash className="mr-3" size={18} />
                            <span className="font-medium">Delete Template</span>
                        </button>
                    </div>

                    {/* Cancel button */}
                    <button
                        className="w-full mt-4 p-3 bg-gray-100 hover:bg-gray-200 rounded-lg text-center font-medium transition-all duration-200 hover:transform hover:scale-[1.02] active:scale-[0.98]"
                        style={{
                            minHeight: '48px'
                        }}
                        onClick={onClose}
                    >
                        Cancel
                    </button>
                </div>
            </div>,
            document.body
        );
    };

    // PropTypes validation for MobileActionMenu
    MobileActionMenu.propTypes = {
        template: PropTypes.shape({
            id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
            name: PropTypes.string,
            card_type_name: PropTypes.string,
        }),
        isOpen: PropTypes.bool,
        onClose: PropTypes.func,
    };

    // Mobile list view component
    const MobileListView = () => {
        if (loading) {
            return (
                <div className="space-y-3 p-4">
                    {[...Array(5)].map((_, index) => (
                        <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm animate-pulse">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center flex-1">
                                    <div className="w-12 h-12 bg-gray-300 rounded-lg mr-3"></div>
                                    <div className="flex-1">
                                        <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                                        <div className="h-3 bg-gray-300 rounded w-1/2 mb-1"></div>
                                        <div className="h-3 bg-gray-300 rounded w-1/3"></div>
                                    </div>
                                </div>
                                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                            </div>
                        </div>
                    ))}
                </div>
            );
        }

        if (!data || data.length === 0) {
            return (
                <div className="flex items-center justify-center min-h-[400px] p-4">
                    <div className="text-center">
                        <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No Templates Found</h3>
                        <p className="text-gray-500 text-sm">
                            {searchQuery || cardTypeFilter !== 'all'
                                ? "No templates match your current filters."
                                : "Start by creating your first template."
                            }
                        </p>
                    </div>
                </div>
            );
        }

        const sortedData = [...data].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

        return (
            <div className="p-4">
                <div className="space-y-3">
                    {sortedData.map((template) => (
                        <div
                            key={template.id}
                            className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm transition-all duration-200 hover:shadow-md hover:border-gray-300 active:scale-[0.98]"
                        >
                            <div className="flex items-center justify-between">
                                {/* Left side - Template info */}
                                <div className="flex items-center flex-1 min-w-0">
                                    {/* Template icon/avatar */}
                                    <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 flex items-center justify-center mr-3 shadow-md flex-shrink-0">
                                        <span className="text-white font-bold text-lg">
                                            {template.name?.charAt(0)?.toUpperCase()}
                                        </span>
                                    </div>

                                    {/* Template details */}
                                    <div className="flex-1 min-w-0">
                                        <h3 className="font-semibold text-gray-900 text-lg truncate mb-1">
                                            {template.name}
                                        </h3>
                                        <div className="flex flex-col gap-1">
                                            <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800 w-fit">
                                                {template.card_type_name}
                                            </span>
                                            <span className="text-sm text-gray-500">
                                                Created {formatDate(template.created_at)}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* Right side - Action button */}
                                <button
                                    className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200 flex-shrink-0 ml-2"
                                    style={{ minHeight: '44px', minWidth: '44px' }}
                                    onClick={() => setMobileActionMenuOpen(template.id)}
                                >
                                    <HiDotsVertical className="text-gray-400" size={20} />
                                </button>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Mobile Action Menu */}
                {mobileActionMenuOpen && (
                    <MobileActionMenu
                        template={sortedData.find(t => t.id === mobileActionMenuOpen)}
                        isOpen={!!mobileActionMenuOpen}
                        onClose={() => setMobileActionMenuOpen(null)}
                    />
                )}
            </div>
        );
    };

    // PropTypes validation for MobileActionMenu
    MobileListView.propTypes = {
        data: PropTypes.arrayOf(PropTypes.shape({
            id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
            name: PropTypes.string,
            card_type_name: PropTypes.string,
        })),
        loading: PropTypes.bool,
        setMobileActionMenuOpen: PropTypes.func,
    };

    // --- Card View for Desktop ---
    const CardListView = () => {
        if (loading) {
            return (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {[...Array(lazyParams?.rows || 6)].map((_, idx) => (
                        <div key={idx} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm animate-pulse min-h-[600px] flex flex-col">
                            {/* Loading Preview */}
                            <div className="flex-1 flex items-center justify-center mb-6">
                                <div className="w-64 h-80 bg-gray-200 rounded-lg animate-pulse"></div>
                            </div>

                            {/* Loading Content */}
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div className="h-6 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                                    <div className="h-6 bg-gray-200 rounded w-1/4 animate-pulse"></div>
                                </div>

                                <div className="space-y-2">
                                    <div className="flex justify-between">
                                        <div className="h-3 bg-gray-200 rounded w-1/3 animate-pulse"></div>
                                        <div className="h-3 bg-gray-200 rounded w-1/4 animate-pulse"></div>
                                    </div>
                                    <div className="flex justify-between">
                                        <div className="h-3 bg-gray-200 rounded w-1/2 animate-pulse"></div>
                                        <div className="h-3 bg-gray-200 rounded w-1/3 animate-pulse"></div>
                                    </div>
                                    <div className="flex justify-between">
                                        <div className="h-3 bg-gray-200 rounded w-1/4 animate-pulse"></div>
                                        <div className="h-3 bg-gray-200 rounded w-1/5 animate-pulse"></div>
                                    </div>
                                </div>

                                {/* Loading Buttons */}
                                <div className="flex gap-2 mt-4">
                                    <div className="flex-1 h-10 bg-gray-200 rounded-lg animate-pulse"></div>
                                    <div className="flex-1 h-10 bg-gray-200 rounded-lg animate-pulse"></div>
                                    <div className="flex-1 h-10 bg-gray-200 rounded-lg animate-pulse"></div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            );
        }
        if (!data || data.length === 0) {
            return (
                <div className="flex items-center justify-center min-h-[400px] text-gray-500 text-lg font-medium">
                    <div className="text-center max-w-md mx-auto">
                        <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                            <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900 mb-3">No Templates Found</h3>
                        <p className="text-gray-600 mb-6 leading-relaxed">
                            {searchQuery || cardTypeFilter !== 'all'
                                ? "No templates match your current filters. Try adjusting your search criteria."
                                : "Start building your design collection by creating your first template."
                            }
                        </p>
                        <button
                            onClick={() => navigate('/manager/design-space')}
                            className="px-6 py-3 bg-white text-black font-medium border border-gray-300 rounded-lg hover:bg-gray-50 hover:border-gray-400 hover:shadow-md hover:transform hover:scale-[1.02] hover:-translate-y-0.5 transition-all duration-200 flex items-center gap-2 mx-auto"
                        >
                            <i className="pi pi-plus text-sm"></i>
                            <span>Create Your First Template</span>
                        </button>
                    </div>
                </div>
            );
        }
        const sortedData = [...data].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        return (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                {(sortedData || []).map((template) => {
                    // استخراج أبعاد التصميم
                    const designDimensions = extractDesignDimensions(template.template);
                    const { width: designWidth, height: designHeight } = designDimensions;

                    // حساب المقياس الأمثل للعرض في البطاقة
                    const containerWidth = 280; // عرض الحاوية المتاح للتصميم
                    const containerHeight = 320; // ارتفاع الحاوية المتاح للتصميم
                    const optimalScale = calculateOptimalScale(designWidth, designHeight, containerWidth, containerHeight);

                    return (
                        <div key={template.id} className="group relative bg-gradient-to-br from-white via-gray-50 to-white border border-gray-100 rounded-3xl shadow-xl hover:shadow-2xl hover:border-gray-200 hover:-translate-y-3 transition-all duration-700 flex flex-col overflow-hidden h-[720px] backdrop-blur-sm transform-gpu">
                            {/* 3D Gradient Overlay */}
                            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                            
                            {/* Top 3D Accent Border */}
                            <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 opacity-0 group-hover:opacity-100 transition-opacity duration-700 rounded-t-3xl"></div>
                            
                            {/* 3D Inner Shadow */}
                            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-black/5 opacity-0 group-hover:opacity-100 transition-opacity duration-700 rounded-3xl"></div>
                            
                            {/* 3D Depth Effect */}
                            <div className="absolute inset-0 bg-gradient-to-br from-white/0 via-white/0 to-black/10 opacity-0 group-hover:opacity-100 transition-opacity duration-700 rounded-3xl"></div>
                            
                                {/* Header Section - Fixed Height */}
                            <div className="flex-shrink-0 px-6 pt-6 pb-4 relative z-10">
                                    <div className="flex items-center justify-between mb-3">
                                    <h3 className="text-xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-transparent truncate flex-1 group-hover:from-blue-900 group-hover:via-purple-900 group-hover:to-pink-900 transition-all duration-700">
                                            {template.name}
                                        </h3>
                                        <div className="flex items-center gap-2 ml-2">
                                        <span className="px-4 py-2 bg-blue-500 text-white rounded-2xl text-xs font-bold shadow-sm transform group-hover:scale-110 group-hover:shadow-md transition-all duration-500">
                                                {template.card_type_name}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* Design Preview - Fixed Height Container with Professional Badge */}
                                <div className="flex-shrink-0 px-4 pb-4 relative group cursor-pointer" style={{ height: '400px' }} onClick={() => {
                                    setSelectedTemplate(template.template);
                                    setTemplateModalVisible(true);
                                    console.log('Open modal for template:', template.name, template.template);
                                }}>
                                    <div className="w-full h-full flex items-center justify-center">
                                        {/* Professional Badge Container - This IS the display area */}
                                        <div className="relative transform group-hover:scale-105 group-hover:-translate-y-3 group-hover:rotate-3 transition-all duration-700">
                                            
                                            {/* Professional Badge */}
                                            <div className="relative">
                                                
                                                {/* Professional Rope */}
                                                <div className="professional-rope-container professional-rope">
                                                    {/* Rope Knot */}
                                                    <div className="rope-knot"></div>
                                                    
                                                    {/* Main Rope */}
                                                    <div className="rope-main">
                                                        <div className="rope-texture-overlay rope-texture"></div>
                                                        <div className="rope-shine-effect rope-shine"></div>
                                                    </div>
                                                    
                                                    {/* Rope to Badge Connection - Enhanced Realistic Loop */}
                                                    <div className="rope-badge-connection rope-connection">
                                                        {/* Loop Edge Highlights */}
                                                        <div className="rope-loop-edge-highlight"></div>
                                                        {/* Loop Inner Edge */}
                                                        <div className="rope-loop-inner-edge"></div>
                                                        {/* Loop Depth Shadow */}
                                                        <div className="rope-loop-depth-shadow"></div>
                                                        {/* Loop Side Highlights */}
                                                        <div className="rope-loop-side-highlight-left"></div>
                                                        <div className="rope-loop-side-highlight-right"></div>
                                                        {/* Loop Inner Side Highlights */}
                                                        <div className="rope-loop-inner-side-left"></div>
                                                        <div className="rope-loop-inner-side-right"></div>
                                                        {/* Loop Corner Accents */}
                                                        <div className="rope-loop-corner-top-left"></div>
                                                        <div className="rope-loop-corner-top-right"></div>
                                                        {/* Loop Inner Corner Accents */}
                                                        <div className="rope-loop-inner-corner-left"></div>
                                                        <div className="rope-loop-inner-corner-right"></div>
                                                        {/* Loop Inner Hollow */}
                                                        <div className="rope-loop-inner"></div>
                                                        {/* Loop Inner Depth */}
                                                        <div className="rope-loop-depth"></div>
                                                        {/* Loop Texture Overlay */}
                                                        <div className="rope-loop-texture"></div>
                                                        {/* Loop Shine Effect */}
                                                        <div className="rope-loop-shine"></div>
                                                        {/* Loop Shadow */}
                                                        <div className="rope-loop-shadow"></div>
                                                    </div>
                                                </div>
                                                
                                                {/* Badge Container - This is the main display area */}
                                                <div className="relative bg-gradient-to-br from-white via-gray-50 to-white rounded-3xl border-2 border-gray-200 shadow-2xl group-hover:shadow-3xl transition-all duration-700 badge-container"
                                            style={{
                                                width: `${designWidth * optimalScale}px`,
                                                height: `${designHeight * optimalScale}px`,
                                                maxWidth: '320px',
                                                maxHeight: '360px',
                                                padding: '0px',
                                                overflow: 'hidden',
                                                position: 'relative',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                boxShadow: '0 12px 40px rgba(0,0,0,0.15), 0 6px 20px rgba(0,0,0,0.1), 0 2px 8px rgba(0,0,0,0.05)',
                                                margin: '0 auto',
                                                         marginTop: '20px'
                                                     }}>
                                                    
                                                    {/* Badge Border Glow */}
                                                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 via-purple-500/20 to-pink-500/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                                    
                                                    {/* 3D Top Edge Highlight */}
                                                    <div className="absolute top-0 left-0 right-0 h-2 bg-gradient-to-r from-white/90 via-white/70 to-white/50 rounded-t-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                                    
                                                    {/* 3D Left Edge Highlight */}
                                                    <div className="absolute top-0 left-0 bottom-0 w-2 bg-gradient-to-b from-white/90 via-white/70 to-white/50 rounded-l-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                                    
                                                    {/* 3D Bottom Shadow */}
                                                    <div className="absolute bottom-0 left-0 right-0 h-3 bg-gradient-to-r from-black/20 via-black/10 to-black/20 rounded-b-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                                    
                                                    {/* 3D Right Shadow */}
                                                    <div className="absolute top-0 right-0 bottom-0 w-3 bg-gradient-to-b from-black/20 via-black/10 to-black/20 rounded-r-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                                    
                                                    {/* 3D Inner Depth Effect */}
                                                    <div className="absolute inset-0 bg-gradient-to-br from-white/0 via-white/0 to-black/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                                                    

                                                    
                                                    {/* Design Content */}
                                                                <div
                                                                    style={{
                                                                        width: `${designWidth}px`,
                                                                        height: `${designHeight}px`,
                                                            transform: `scale(${optimalScale})`,
                                                                        transformOrigin: 'center center',
                                                                        pointerEvents: 'none',
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        justifyContent: 'center',
                                                            filter: 'drop-shadow(0 8px 16px rgba(0,0,0,0.15))',
                                                                        background: template.background || parseBackgroundStyle(template.background_style) || '#ffffff',
                                                                    }}
                                                                >
                                                                    {template.template ? (
                                                                        <div style={{
                                                                            width: '100%',
                                                                            height: '100%',
                                                                background: template.background || parseBackgroundStyle(template.background_style) || '#ffffff'
                                                                        }}>{parse(hideBraces(template.template))}</div>
                                                                    ) : (
                                                                        <div className="text-gray-400 text-center">No design</div>
                                                                    )}
                                            </div>

                                            {/* Enhanced 3D Overlay */}
                                            <div
                                                        className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-500/0 via-purple-500/0 to-pink-500/0 group-hover:from-blue-500/15 group-hover:via-purple-500/15 group-hover:to-pink-500/15 transition-all duration-500 rounded-3xl"
                                                style={{ pointerEvents: 'none' }}
                                            >
                                                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                                                    <div className="text-white opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-120 transition-all duration-500">
                                                        <div className="bg-white/25 backdrop-blur-md rounded-full p-4 shadow-2xl border border-white/30">
                                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                                                            </svg>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                                    
                                                    {/* 3D Reflection Effect */}
                                                    <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 rounded-3xl"></div>
                                                    
                                                    {/* Small Hollow Opening at Top of Badge */}
                                                    <div className="absolute top-2 left-1/2 transform -translate-x-1/2 z-20">
                                                        <div className="relative">
                                                            {/* Small Hollow Professional Design */}
                                                            <div className="relative w-20 h-6 hollow-area">
                                                                {/* Outer Ring - Professional Design */}
                                                                <div className="absolute inset-0 bg-gradient-to-b from-slate-800 via-slate-700 to-slate-600 rounded-full shadow-lg border border-slate-500"
                                                                     style={{
                                                                         background: 'linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%)',
                                                                         boxShadow: '0 4px 12px rgba(30, 41, 59, 0.4), inset 0 1px 4px rgba(255,255,255,0.1)',
                                                                         border: '2px solid #64748b'
                                                                     }}>
                                                                </div>
                                                                
                                                                {/* Inner Hollow Area - Completely Transparent */}
                                                                <div className="absolute inset-2 bg-transparent rounded-full"
                                                                     style={{
                                                                         background: 'transparent',
                                                                         boxShadow: 'none'
                                                                     }}>
                                                                </div>
                                                                
                                                                {/* Professional Accent Lines */}
                                                                <div className="absolute top-0.5 left-1/2 transform -translate-x-1/2 w-16 h-0.5 bg-gradient-to-r from-slate-400 to-transparent rounded-full opacity-60"></div>
                                                                <div className="absolute bottom-0.5 left-1/2 transform -translate-x-1/2 w-14 h-0.5 bg-gradient-to-r from-transparent to-slate-300 rounded-full opacity-40"></div>
                                                                
                                                                {/* Rope Passage Opening - Completely Open */}
                                                                <div className="absolute inset-2 bg-transparent rounded-full opacity-100 rope-passage rope-entering"
                                                                     style={{
                                                                         background: 'transparent',
                                                                         boxShadow: 'none'
                                                                     }}>
                                                                    {/* Rope Entry Glow Effect */}
                                                                    <div className="absolute inset-0 bg-gradient-to-b from-gray-500/20 via-gray-400/10 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                                                    
                                                                    {/* Rope Inside Hollow - Professional Design - Always Visible */}
                                                                    <div className="absolute inset-0 flex items-center justify-center">
                                                                                                                                                 {/* Rope Segment Inside - Centered */}
                                                                         <div className="relative w-16 h-4 bg-gradient-to-b from-white via-gray-100 to-gray-200 rounded-full opacity-95"
                                                                              style={{
                                                                                  background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #e2e8f0 100%)',
                                                                                  boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.1), 0 1px 2px rgba(255,255,255,0.8)',
                                                                                  border: '1px solid rgba(255, 255, 255, 0.5)'
                                                                              }}>
                                                                            {/* Rope Texture Inside */}
                                                                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent opacity-60"
                                                                                 style={{
                                                                                     background: 'repeating-linear-gradient(90deg, transparent 0px, transparent 1px, rgba(255, 255, 255, 0.3) 1px, rgba(255, 255, 255, 0.3) 2px)'
                                                                                 }}>
                                                                            </div>
                                                                            {/* Rope Shine Inside */}
                                                                            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/20 to-transparent rounded-full"></div>
                                                                            
                                                                            {/* Rope Edge Highlights */}
                                                                            <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-white to-gray-100 rounded-t-full opacity-60"></div>
                                                                            <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-gray-200 to-gray-300 rounded-b-full opacity-40"></div>
                                                                        </div>
                                                                        
                                                                        {/* Rope Shadow Inside - Centered */}
                                                                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-14 h-2 bg-black/20 rounded-full blur-sm"></div>
                                                                        
                                                                        {/* Rope Reflection Inside - Centered */}
                                                                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-12 h-1 bg-white/20 rounded-full blur-sm"></div>
                                                                        
                                                                        {/* Rope Depth Effect Inside - Centered */}
                                                                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-14 h-3 bg-black/10 rounded-full blur-sm"></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                {/* Badge Shadow */}
                                                <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 w-72 h-6 bg-gradient-to-r from-black/30 via-black/20 to-black/30 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                                                
                                                {/* Template Name Badge Label */}
                                                <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 z-30">
                                                    <div className="px-3 py-1 bg-gradient-to-r from-slate-600 via-slate-700 to-slate-800 text-white text-xs font-bold rounded-full shadow-lg border border-slate-500 transform group-hover:scale-110 transition-all duration-500"
                                                         style={{
                                                             background: 'linear-gradient(135deg, #475569 0%, #334155 50%, #1e293b 100%)',
                                                             boxShadow: '0 4px 12px rgba(71, 85, 105, 0.4)',
                                                             textShadow: '0 1px 2px rgba(0,0,0,0.3)'
                                                         }}>
                                                        <div className="flex items-center gap-1">
                                                            <div className="w-1.5 h-1.5 bg-slate-300 rounded-full opacity-80"></div>
                                                            {template.name}
                                                            <div className="w-1.5 h-1.5 bg-slate-300 rounded-full opacity-80"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                {/* Decorative Corner Elements - Gray Only */}
                                                <div className="absolute top-4 left-4 w-3 h-3 bg-gradient-to-br from-slate-400 to-slate-600 rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-500 shadow-sm"></div>
                                                <div className="absolute top-4 right-4 w-3 h-3 bg-gradient-to-br from-slate-400 to-slate-600 rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-500 shadow-sm"></div>
                                                <div className="absolute bottom-4 left-4 w-3 h-3 bg-gradient-to-br from-slate-400 to-slate-600 rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-500 shadow-sm"></div>
                                                <div className="absolute bottom-4 right-4 w-3 h-3 bg-gradient-to-br from-slate-400 to-slate-600 rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-500 shadow-sm"></div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                {/* Info Section - Fixed Height */}
                            <div className="flex-shrink-0 px-6 pb-4 relative z-10">
                                    <div className="space-y-3">
                                        <div className="flex items-center justify-between text-sm">
                                            <span className="text-gray-500 font-medium">Created</span>
                                        <span className="text-gray-700 font-semibold bg-gradient-to-r from-gray-700 to-gray-900 bg-clip-text text-transparent">
                                                {formatDate(template.created_at)}
                                            </span>
                                        </div>

                                        <div className="flex items-center justify-between text-sm">
                                            <span className="text-gray-500 font-medium">Last Modified</span>
                                        <span className="text-gray-700 font-semibold bg-gradient-to-r from-gray-700 to-gray-900 bg-clip-text text-transparent">
                                                {formatDate(template.updated_at)}
                                            </span>
                                        </div>

                                        <div className="flex items-start justify-between text-sm">
                                            <span className="text-gray-500 font-medium mt-1">Groups</span>
                                            <div className="flex flex-col gap-1.5 max-w-40">
                                                {template.groups && template.groups.length > 0 ? (
                                                    <>
                                                        {template.groups.slice(0, 2).map((group) => (
                                                            <div
                                                                key={group.id}
                                                            className="flex items-center gap-2 px-2.5 py-1.5 bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 rounded-xl text-xs font-medium text-blue-700 hover:from-blue-100 hover:to-blue-200 transition-all duration-300 cursor-pointer shadow-sm"
                                                                title={`${group.title}${group.description ? ` - ${group.description}` : ''}`}
                                                            >
                                                            <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex-shrink-0 shadow-sm"></div>
                                                                <span className="truncate font-semibold">{group.title}</span>
                                                                {group.design_type && (
                                                                <span className="px-1.5 py-0.5 bg-gradient-to-r from-blue-200 to-blue-300 border border-blue-300 rounded-lg text-[10px] font-bold text-blue-800 ml-auto shadow-sm">
                                                                        {group.design_type}
                                                                    </span>
                                                                )}
                                                            </div>
                                                        ))}
                                                        {template.groups.length > 2 && (
                                                        <div className="relative group/more">
                                                            <div className="flex items-center gap-2 px-2.5 py-1.5 bg-gradient-to-r from-slate-100 to-slate-200 border border-slate-300 rounded-xl text-xs font-medium text-slate-700 shadow-sm hover:from-slate-200 hover:to-slate-300 hover:border-slate-400 hover:shadow-md transition-all duration-300 cursor-pointer">
                                                                <div className="w-2 h-2 bg-gradient-to-r from-slate-500 to-slate-600 rounded-full flex-shrink-0 shadow-sm"></div>
                                                                <span className="font-semibold">
                                                                    +{template.groups.length - 2} more
                                                                </span>
                                                                <svg className="w-3 h-3 text-slate-500 transition-transform duration-300 group-hover/more:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                                </svg>
                                                            </div>
                                                            
                                                            {/* Dropdown with remaining groups */}
                                                            <div className="absolute bottom-full left-0 right-0 mb-2 bg-white border border-slate-200 rounded-lg shadow-xl opacity-0 invisible group-hover/more:opacity-100 group-hover/more:visible transition-all duration-300 z-50 max-h-32 overflow-y-auto">
                                                                <div className="p-2 space-y-1">
                                                                    {template.groups.slice(2).map((group) => (
                                                                        <div key={group.id} className="flex items-center gap-2 px-2 py-1.5 bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 rounded-lg text-xs font-medium text-blue-700 hover:from-blue-100 hover:to-blue-200 transition-all duration-200">
                                                                            <div className="w-1.5 h-1.5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex-shrink-0 shadow-sm"></div>
                                                                            <span className="truncate font-semibold">{group.title}</span>
                                                                            {group.design_type && (
                                                                                <span className="px-1 py-0.5 bg-gradient-to-r from-blue-200 to-blue-300 border border-blue-300 rounded text-[9px] font-bold text-blue-800 ml-auto shadow-sm">
                                                                                    {group.design_type}
                                                                                </span>
                                                                            )}
                                                                        </div>
                                                                    ))}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        )}
                                                    </>
                                                ) : (
                                                <div className="flex items-center gap-2 px-2.5 py-1.5 bg-gradient-to-r from-emerald-50 to-emerald-100 border border-emerald-200 rounded-xl text-xs font-medium text-emerald-700 shadow-sm hover:from-emerald-100 hover:to-emerald-200 hover:border-emerald-300 hover:shadow-md transition-all duration-300">
                                                    <div className="w-2 h-2 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full flex-shrink-0 shadow-sm"></div>
                                                    <span className="font-semibold">All Groups</span>
                                                    <svg className="w-3 h-3 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                                    </svg>
                                                </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                {/* Action Buttons - Fixed at Bottom */}
                            <div className="flex-shrink-0 px-6 pb-6 mt-auto relative z-10">
                                <div className="flex gap-3">
                                    {/* Edit Button - 3D Professional */}
                                        <button
                                        className="flex-1 relative group/btn overflow-hidden rounded-2xl bg-gradient-to-br from-emerald-500 via-green-500 to-emerald-600 p-0.5 shadow-lg hover:shadow-xl transition-all duration-500 hover:scale-105 hover:-translate-y-1"
                                            onClick={() => navigate(`/manager/design-space/${template.id}`)}
                                        >
                                        <div className="relative flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl text-white font-bold text-sm transition-all duration-300 group-hover/btn:from-emerald-400 group-hover/btn:to-green-500">
                                            <div className="relative z-10">
                                                <FiEdit size={16} className="text-white drop-shadow-sm" />
                                            </div>
                                            <span className="relative z-10 drop-shadow-sm">Edit</span>
                                            {/* 3D Shine Effect */}
                                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover/btn:translate-x-full transition-transform duration-1000"></div>
                                        </div>
                                        </button>

                                    {/* Name Button - 3D Professional */}
                                        <button
                                        className="flex-1 relative group/btn overflow-hidden rounded-2xl bg-gradient-to-br from-blue-500 via-indigo-500 to-blue-600 p-0.5 shadow-lg hover:shadow-xl transition-all duration-500 hover:scale-105 hover:-translate-y-1"
                                            onClick={() => openEditNameModal(template)}
                                        >
                                        <div className="relative flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl text-white font-bold text-sm transition-all duration-300 group-hover/btn:from-blue-400 group-hover/btn:to-indigo-500">
                                            <div className="relative z-10">
                                                <FiEdit size={16} className="text-white drop-shadow-sm" />
                                            </div>
                                            <span className="relative z-10 drop-shadow-sm">Name</span>
                                            {/* 3D Shine Effect */}
                                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover/btn:translate-x-full transition-transform duration-1000"></div>
                                        </div>
                                        </button>

                                    {/* Delete Button - 3D Professional */}
                                        <button
                                        className="flex-1 relative group/btn overflow-hidden rounded-2xl bg-gradient-to-br from-red-500 via-rose-500 to-red-600 p-0.5 shadow-lg hover:shadow-xl transition-all duration-500 hover:scale-105 hover:-translate-y-1"
                                            onClick={() => confirmDeleteTemplate(template.id)}
                                        >
                                        <div className="relative flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-br from-red-500 to-rose-600 rounded-2xl text-white font-bold text-sm transition-all duration-300 group-hover/btn:from-red-400 group-hover/btn:to-rose-500">
                                            <div className="relative z-10">
                                                <TfiTrash size={16} className="text-white drop-shadow-sm" />
                                    </div>
                                            <span className="relative z-10 drop-shadow-sm">Delete</span>
                                            {/* 3D Shine Effect */}
                                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover/btn:translate-x-full transition-transform duration-1000"></div>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    );
                })}
            </div>
        );
    };

    const buildUrlWithFilters = (searchQuery, cardTypeFilter) => {
        let filterQuery = '';
        if (searchQuery.trim()) {
            filterQuery += `filter[name]=${encodeURIComponent(searchQuery.trim())}`;
        }
        if (cardTypeFilter !== 'all') {
            if (filterQuery.length > 0) filterQuery += '&';
            filterQuery += `filter[card_type_id]=${encodeURIComponent(cardTypeFilter)}`;
        }
        if (filterQuery.length > 0) filterQuery += '&';
        filterQuery += 'sort=-created_at';

        let url = 'get-designs-list';
        if (filterQuery.length > 0) {
            url += `?${filterQuery}`;
        }
        return url;
    };

    // تحقق من الاشتراك قبل أي عرض للصفحة
    if (subscriptionLoading) {
        return <p className="text-center">Checking subscription...</p>;
    }
    if (subscriptionError && subscriptionError.message === "Your subscription has expired. Please renew your subscription to continue.") {
        return (
            <Container>
                <div className="relative isolate bg-white px-6 py-12 sm:py-16 lg:px-8">
                    <div className="absolute inset-x-0 -top-3 -z-10 transform-gpu overflow-hidden px-36 blur-3xl" aria-hidden="true">
                        {/* You can copy the same motion.div from Groups page if you use framer-motion */}
                    </div>
                    <div className="mx-auto max-w-4xl text-center">
                        <h2 className="text-base font-semibold leading-7 text-indigo-600">Subscription Status</h2>
                        <h1 className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
                            Subscription Expired
                        </h1>
                        <p className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-gray-600">
                            {subscriptionError.message}
                        </p>
                        <p className="mt-4 text-center text-gray-600">
                            Please renew your subscription to continue using the service.
                        </p>
                    </div>
                    <div className="mt-16 mx-auto max-w-2xl">
                        <div className="relative rounded-3xl p-8 ring-1 ring-gray-900/10 shadow-xl bg-gradient-to-br from-red-50 to-white"
                            style={{ borderTop: '6px solid #ef4444' }}>
                            <h3 className="text-2xl font-bold text-gray-900 mb-2">Expired Plan</h3>
                            <div className="flex items-center">
                                <svg className="h-5 w-5 flex-none text-red-600" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
                                </svg>
                                <span className="ml-3 text-red-600 font-medium">Your subscription has expired</span>
                            </div>
                            <div className="mt-6 p-3 rounded-lg bg-red-100 text-red-800 flex items-center justify-center">
                                <span className="font-medium">⚠️ Please renew your subscription to continue</span>
                            </div>
                            <div className="mt-10 flex items-center justify-center gap-x-6">
                                <button
                                    onClick={() => navigate('/manager/Packages')}
                                    className="main-btn text-md shadow-md px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200"
                                >
                                    Renew Now
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </Container>
        );
    }
    if (noPackage) {
        return (
            <Container>
                <div className="relative isolate bg-white px-6 py-12 sm:py-16 lg:px-8">
                    <div className="absolute inset-x-0 -top-3 -z-10 transform-gpu overflow-hidden px-36 blur-3xl" aria-hidden="true">
                        {/* You can copy the same motion.div from Groups page if you use framer-motion */}
                    </div>
                    <div className="mx-auto max-w-4xl text-center">
                        <h2 className="text-base font-semibold leading-7 text-indigo-600">No Package Found</h2>
                        <h1 className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
                            Unlock All Features With a Package
                        </h1>
                        <p className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-gray-600">
                            We couldn&apos;t find any active package for your account. To access all features and design templates, please purchase a package.
                        </p>
                        <p className="mt-4 text-center text-gray-600">
                            Click the button below to explore available packages and unlock the full potential of your dashboard.
                        </p>
                    </div>
                    <div className="mt-16 mx-auto max-w-2xl">
                        <div className="relative rounded-3xl p-8 ring-1 ring-gray-900/10 shadow-xl bg-gradient-to-br from-blue-50 to-white"
                            style={{ borderTop: '6px solid #3b82f6' }}>
                            <h3 className="text-2xl font-bold text-gray-900 mb-2">No Active Package</h3>
                            <div className="flex items-center">
                                <svg className="h-5 w-5 flex-none text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
                                </svg>
                                <span className="ml-3 text-blue-600 font-medium">No package is currently assigned to your account</span>
                            </div>
                            <div className="mt-6 p-3 rounded-lg bg-blue-100 text-blue-800 flex items-center justify-center">
                                <span className="font-medium">💡 Purchase a package to unlock all features</span>
                            </div>
                            <div className="mt-10 flex items-center justify-center gap-x-6">
                                <button
                                    onClick={() => navigate('/manager/Packages')}
                                    className="main-btn text-md shadow-md px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200"
                                >
                                    View Packages
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </Container>
        );
    }

    return (

        <Container>
            <Toast ref={toastRef} />
            <ConfirmDialog />

            {/* إضافة CSS مخصص للـ dropdown */}
            <style>{dropdownStyles}</style>

            {/* Edit Name Modal */}
            <Dialog
                visible={editNameModal.open}
                onHide={closeEditNameModal}
                header="Edit Template Name"
                style={{ width: '400px' }}
                modal
                className="edit-name-dialog"
                draggable={false}
                resizable={false}
                closeOnEscape
                closeOnBackdropClick
            >
                <div className="p-4">
                    <div className="mb-4">
                        <label htmlFor="templateName" className="block text-sm font-medium text-gray-700 mb-2">
                            Template Name
                        </label>
                        <input
                            id="templateName"
                            type="text"
                            value={newTemplateName}
                            onChange={(e) => setNewTemplateName(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Enter template name"
                            autoFocus
                        />
                    </div>
                    <div className="flex justify-end gap-2">
                        <button
                            onClick={closeEditNameModal}
                            className="px-4 py-2 bg-white text-black font-medium border border-gray-300 rounded-lg hover:bg-gray-50 hover:border-gray-400 hover:shadow-md hover:transform hover:scale-[1.02] hover:-translate-y-0.5 transition-all duration-200"
                        >
                            Cancel
                        </button>
                        <button
                            onClick={handleEditNameSave}
                            disabled={!newTemplateName.trim()}
                            className="px-4 py-2 bg-white text-black font-medium border border-gray-300 rounded-lg hover:bg-gray-50 hover:border-gray-400 hover:shadow-md hover:transform hover:scale-[1.02] hover:-translate-y-0.5 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:translate-y-0"
                            style={{
                                backgroundColor: !newTemplateName.trim() ? '#f3f4f6' : '#00c3ac',
                                color: !newTemplateName.trim() ? '#9ca3af' : 'white',
                                borderColor: !newTemplateName.trim() ? '#d1d5db' : '#00c3ac'
                            }}
                        >
                            Save
                        </button>
                    </div>
                </div>
            </Dialog>

            <Dialog
                visible={templateModalVisible}
                onHide={() => {
                    setTemplateModalVisible(false);
                    setTemplateZoomLevel(1.0);
                }}
                header={
                    <div className="flex justify-between items-center w-full">
                        <span className="mr-1">Template Preview</span>
                        <div className="flex items-center gap-2 mr-8">
                            <button
                                className="p-2 rounded-full hover:bg-gray-200"
                                onClick={() => setTemplateZoomLevel(prev => Math.max(0.5, prev - 0.1))}
                                title="Zoom Out"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </button>
                            <span className="mx-2 text-gray-600">Zoom: {Math.round(templateZoomLevel * 100)}%</span>
                            <button
                                className="p-2 rounded-full hover:bg-gray-200"
                                onClick={() => setTemplateZoomLevel(prev => Math.min(2.0, prev + 0.1))}
                                title="Zoom In"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                            </button>
                        </div>
                    </div>
                }
                style={{ width: '95vw', maxWidth: '1400px', height: '90vh' }}
                modal
                className="template-preview-dialog"
                contentClassName="p-0 overflow-hidden"
                contentStyle={{ 
                    height: 'calc(90vh - 120px)', 
                    overflow: 'hidden', 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'center',
                    padding: '20px'
                }}
                draggable={false}
                resizable={false}
                maximizable={false}
                closeOnEscape
                closeOnBackdropClick
            >
                { (
                    <div
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: '100%',
                            height: '100%',
                            background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)',
                            borderRadius: '12px',
                            padding: '20px',
                            overflow: 'hidden'
                        }}
                    >
                        {selectedTemplate && (() => {
                            const designDimensions = extractDesignDimensions(selectedTemplate);
                            const { width: designWidth, height: designHeight } = designDimensions;
                            
                            const modalContentWidth = Math.min(1400, window.innerWidth * 0.95) - 40; 
                            const modalContentHeight = (window.innerHeight * 0.9) - 160; 
                            const optimalScale = calculateOptimalScale(designWidth, designHeight, modalContentWidth, modalContentHeight);
                            
                            const originalTemplate = data?.find(t => t.template === selectedTemplate);
                            
                            return (
                        <div
                            style={{
                                        boxShadow: '0 12px 40px rgba(0,0,0,0.2)',
                                        background: originalTemplate?.background || parseBackgroundStyle(originalTemplate?.background_style) || '#ffffff',
                                        borderRadius: '20px',
                                        padding: '0px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                        width: `${designWidth * optimalScale * templateZoomLevel}px`,
                                        height: `${designHeight * optimalScale * templateZoomLevel}px`,
                                maxWidth: '100%',
                                        maxHeight: '100%',
                                        overflow: 'hidden',
                                transformOrigin: 'center center',
                                        transition: 'transform 0.3s ease-in-out',
                                        border: '2px solid rgba(0,0,0,0.1)'
                            }}
                        >
                                    <div style={{
                                        width: '100%',
                                        height: '100%',
                                        background: originalTemplate?.background || parseBackgroundStyle(originalTemplate?.background_style) || '#ffffff',
                                        transform: `scale(${optimalScale * templateZoomLevel})`,
                                        transformOrigin: 'center center',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center'
                                    }}>
                                        {parse(hideBraces(selectedTemplate))}
                        </div>
                                </div>
                            );
                        })()}
                    </div>
                )}
            </Dialog>

            {/* Main Content with Theme Colors */}
            <div className="min-h-screen bg-gray-50 relative">
                <div className="p-4 md:p-8">
                    {/* Professional Header Section */}
                    <div className="w-full mb-8">
                        <div className="w-full">
                            {/* Header Background with Light Gradient */}
                            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-50 via-purple-50 to-pink-50 shadow-xl border border-gray-200">
                                {/* Animated Background Pattern */}
                                <div className="absolute inset-0 opacity-5">
                                    <div className="absolute inset-0" style={{
                                        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                                        backgroundSize: '60px 60px'
                                    }}></div>
                                </div>

                                {/* Floating Elements */}
                                <div className="absolute top-4 right-4 w-20 h-20 bg-purple-200/30 rounded-full blur-xl"></div>
                                <div className="absolute bottom-4 left-4 w-16 h-16 bg-pink-200/30 rounded-full blur-xl"></div>

                                {/* Header Content */}
                                <div className="relative z-10 p-4">
                                    {/* Title Section - Improved Mobile Layout */}
                                    <div className={`${isMobile ? 'flex flex-col space-y-3 mb-4' : 'flex flex-row items-center justify-between mb-4'}`}>
                                        <div className={`${isMobile ? 'w-full' : 'mb-0'}`}>
                                            <div className={`flex items-center gap-3 ${isMobile ? 'justify-center' : ''}`}>
                                                <div className="relative">
                                                    <div className={`${isMobile ? 'w-10 h-10' : 'w-12 h-12'} bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center shadow-lg`}>
                                                        <FaPalette className={`text-white ${isMobile ? 'text-base' : 'text-lg'}`} />
                                                    </div>
                                                    <div className={`absolute -top-1 -right-1 ${isMobile ? 'w-4 h-4' : 'w-5 h-5'} bg-green-500 rounded-full flex items-center justify-center border-2 border-white shadow-md`}>
                                                        <span className={`text-white ${isMobile ? 'text-xs' : 'text-xs'} font-bold`}>
                                                            {filteredData?.length || 0}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className={`${isMobile ? 'text-center' : ''}`}>
                                                    <h1 className={`${isMobile ? 'text-lg' : 'text-xl lg:text-2xl'} font-bold text-gray-900 mb-0.5`}>
                                                        Templates Management
                                                    </h1>
                                                    <p className={`text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                                                        {isMobile ? 'Manage your templates' : 'Create and organize your design templates efficiently'}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Desktop Create Button */}
                                        {!isMobile && (
                                            <button
                                                onClick={() => navigate('/manager/design-space')}
                                                className="group relative px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-600 text-white font-semibold rounded-xl hover:from-purple-600 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 flex items-center gap-2 border border-purple-400/30"
                                            >
                                                <div className="relative">
                                                    <FaPlus className="text-base group-hover:rotate-90 transition-transform duration-300" />
                                                </div>
                                                <span className="text-base">Add New Template</span>
                                                <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 to-pink-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                            </button>
                                        )}
                                    </div>

                                    {/* Search and Filters Section - Improved Mobile Layout */}
                                    <div className={`${isMobile ? 'space-y-3' : 'grid grid-cols-1 lg:grid-cols-12 gap-3 items-end'}`}>
                                        {/* Search Input - Mobile Optimized */}
                                        <div className={`relative ${isMobile ? 'w-full order-1' : 'lg:col-span-6'}`}>
                                            <div className="relative">
                                                <div className={`absolute inset-y-0 left-0 ${isMobile ? 'pl-3' : 'pl-4'} flex items-center pointer-events-none z-10`}>
                                                    <div className={`${isMobile ? 'w-6 h-6' : 'w-8 h-8'} bg-gradient-to-br from-slate-700 via-slate-600 to-slate-800 rounded-xl flex items-center justify-center shadow-lg border border-slate-500/30 group-hover:scale-110 transition-all duration-300 hover:shadow-xl hover:from-slate-600 hover:via-slate-500 hover:to-slate-700`}>
                                                        <svg className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-white group-hover:scale-110 transition-transform duration-300 group-hover:rotate-12`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                        </svg>
                                                        <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                                    </div>
                                                </div>
                                                <input
                                                    type="text"
                                                    placeholder={isMobile ? "Search templates..." : "Search templates by name, description..."}
                                                    className={`w-full ${isMobile ? 'pl-12 pr-3 py-2' : 'pl-16 pr-4 py-2.5'} bg-white/90 backdrop-blur-sm border border-gray-200 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-purple-400 transition-all duration-300 ${isMobile ? 'text-sm' : 'text-sm'} shadow-sm group`}
                                                    value={searchQuery}
                                                    onChange={(e) => setSearchQuery(e.target.value)}
                                                />
                                                <div className="absolute inset-0 bg-gradient-to-r from-purple-50/50 to-pink-50/50 rounded-xl opacity-0 focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                                            </div>
                                        </div>

                                        {/* Filter Dropdown - Mobile Optimized */}
                                        <div className={`${isMobile ? 'w-full order-2' : 'lg:col-span-3'}`}>
                                            <div className="relative">
                                                <label className={`block text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'} font-medium ${isMobile ? 'mb-1' : 'mb-2'} flex items-center gap-1`}>
                                                    <div className="relative group">
                                                        <MdCategory className={`text-purple-600 group-hover:text-purple-700 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6`} size={isMobile ? 14 : 18} />
                                                        <div className="absolute inset-0 bg-purple-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
                                                    </div>
                                                    {isMobile ? 'Type' : 'Card Type'}
                                                </label>
                                                <div className="relative">
                                                    <Dropdown
                                                        value={cardTypeFilter}
                                                        options={cardTypeOptions}
                                                        onChange={(e) => setCardTypeFilter(e.value)}
                                                        placeholder="Filter by type"
                                                        className={`w-full ${isMobile ? 'text-xs' : 'text-sm'}`}
                                                        panelClassName="templates-dropdown-panel"
                                                        showClear={cardTypeFilter !== 'all'}
                                                        emptyMessage="No card types available"
                                                    />
                                                </div>
                                            </div>
                                        </div>

                                        {/* Stats Section - Mobile Optimized */}
                                        <div className={`${isMobile ? 'order-3 mt-2' : 'lg:col-span-3'}`}>
                                            <div className={`grid grid-cols-2 ${isMobile ? 'gap-2' : 'gap-3'}`}>
                                                {/* Total Templates */}
                                                <div className={`bg-white/80 backdrop-blur-sm rounded-xl ${isMobile ? 'p-1.5' : 'p-2'} border border-gray-200 shadow-sm hover:shadow-lg transition-all duration-300 group`}>
                                                    <div className="text-center">
                                                        <div className={`flex items-center justify-center ${isMobile ? 'gap-1 mb-0.5' : 'gap-2 mb-1'}`}>
                                                            <div className="relative group">
                                                                <FaPalette className={`text-purple-600 group-hover:text-purple-700 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6`} size={isMobile ? 14 : 20} />
                                                                <div className="absolute inset-0 bg-purple-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                                                            </div>
                                                            <div className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-gray-900 group-hover:text-purple-700 transition-colors duration-300`}>
                                                                {filteredData?.length || 0}
                                                            </div>
                                                        </div>
                                                        <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-600 group-hover:text-purple-600 transition-colors duration-300`}>
                                                            {isMobile ? 'Total' : 'Templates'}
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Card Types */}
                                                <div className={`bg-white/80 backdrop-blur-sm rounded-xl ${isMobile ? 'p-1.5' : 'p-2'} border border-gray-200 shadow-sm hover:shadow-lg transition-all duration-300 group`}>
                                                    <div className="text-center">
                                                        <div className={`flex items-center justify-center ${isMobile ? 'gap-1 mb-0.5' : 'gap-2 mb-1'}`}>
                                                            <div className="relative group">
                                                                <BsCardList className={`text-pink-600 group-hover:text-pink-700 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6`} size={isMobile ? 14 : 20} />
                                                                <div className="absolute inset-0 bg-pink-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                                                            </div>
                                                            <div className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-pink-600 group-hover:text-pink-700 transition-colors duration-300`}>
                                                                {cardTypeOptions?.length - 1 || 0}
                                                            </div>
                                                        </div>
                                                        <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-600 group-hover:text-pink-600 transition-colors duration-300`}>Types</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Quick Actions */}
                                    <div className="mt-6 flex flex-wrap gap-3">
                                        <button
                                            onClick={() => {
                                                setSearchQuery('');
                                                setCardTypeFilter('all');
                                            }}
                                            className="px-4 py-2 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-lg text-gray-700 hover:bg-white hover:border-gray-300 transition-all duration-300 text-sm font-medium shadow-sm flex items-center gap-2 group"
                                        >
                                            <div className="relative">
                                                <MdClear className="text-gray-600 group-hover:text-gray-800 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-12" size={16} />
                                                <div className="absolute inset-0 bg-gray-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                                            </div>
                                            Clear Filters
                                        </button>
                                        <div className="px-4 py-2 bg-purple-100/80 backdrop-blur-sm border border-purple-200 rounded-lg text-purple-700 text-sm font-medium shadow-sm flex items-center gap-2 group">
                                            <div className="relative">
                                                <MdTrendingUp className="text-purple-600 group-hover:text-purple-700 transition-colors duration-300 group-hover:scale-110 group-hover:-rotate-12" size={16} />
                                                <div className="absolute inset-0 bg-purple-200 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                                            </div>
                                            Showing {filteredData?.length || 0} of {data?.length || 0} templates
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Content Area */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        {isMobile ? (
                            <MobileListView />
                        ) : (
                            <div className="p-6">
                                <CardListView />
                            </div>
                        )}
                    </div>

                    {/* Pagination Controls */}
                    {!isMobile && !loading && data && data.length > 0 && (
                        <div className="flex justify-center mt-6">
                            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                                <div className="flex items-center gap-4">
                                    <button
                                        onClick={() => {
                                            const newFirst = Math.max(0, (lazyParams?.first || 0) - (lazyParams?.rows || 12));
                                            const newPage = Math.floor(newFirst / 12);
                                            dataHandler({
                                                first: newFirst,
                                                page: newPage,
                                                rows: 12,
                                                url: buildUrlWithFilters(searchQuery, cardTypeFilter)
                                            });
                                        }}
                                        disabled={!lazyParams?.first || lazyParams.first === 0}
                                        className="px-4 py-2 text-sm bg-white text-black font-medium border border-gray-300 rounded-lg hover:bg-gray-50 hover:border-gray-400 hover:shadow-md hover:transform hover:scale-[1.02] hover:-translate-y-0.5 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:translate-y-0"
                                    >
                                        Previous
                                    </button>
                                    <span className="px-4 py-2 text-sm text-gray-700 font-medium">
                                        {Math.floor((lazyParams?.first || 0) / (lazyParams?.rows || 12)) + 1} of {Math.ceil(totalRecords / (lazyParams?.rows || 12))}
                                    </span>
                                    <button
                                        onClick={() => {
                                            const newFirst = (lazyParams?.first || 0) + 12;
                                            const newPage = Math.floor(newFirst / 12);
                                            dataHandler({
                                                first: newFirst,
                                                page: newPage,
                                                rows: 12,
                                                url: buildUrlWithFilters(searchQuery, cardTypeFilter)
                                            });
                                        }}
                                        disabled={(lazyParams?.first || 0) + 12 >= totalRecords}
                                        className="px-4 py-2 text-sm bg-white text-black font-medium border border-gray-300 rounded-lg hover:bg-gray-50 hover:border-gray-400 hover:shadow-md hover:transform hover:scale-[1.02] hover:-translate-y-0.5 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:translate-y-0"
                                    >
                                        Next
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Mobile Floating Action Button (FAB) */}
            {isMobile && (
                <div className="mobile-fab-container">
                    <button
                        onClick={() => navigate('/manager/design-space')}
                        className="mobile-fab"
                        title="Add New Template"
                    >
                        <i className="pi pi-plus text-xl"></i>
                    </button>
                </div>
            )}
        </Container>
    );
}

export default TemplatesDataTable;